// @ts-nocheck
/**
 * 订阅功能管理服务
 * 处理不同订阅级别的功能差异
 */

import { UserSubscription } from '../types/api';

// 订阅计划类型
export type SubscriptionPlanType = 'free' | 'basic_monthly' | 'pro_monthly' | 'unlimited_monthly' | 'pro_yearly';

// 订阅功能配置
export interface SubscriptionFeatures {
  // AI模型配置
  aiModel: {
    model: string;           // AI模型名称
    temperature: number;     // 创意度
    topK: number;            // 多样性
    maxOutputTokens: number; // 最大输出长度
  };
  
  // 图片生成配置
  imageGeneration: {
    model: string;           // 图片模型
    resolution: string;      // 分辨率
    quality: string;         // 质量级别
    maxRetries: number;      // 最大重试次数
    enhancedPrompt: boolean; // 是否使用增强提示词
  };
  
  // 音频生成配置
  audioGeneration: {
    model: string;           // 音频模型
    quality: string;         // 音质
    voiceOptions: string[];  // 可用声音选项
  };
  
  // 用户限制
  limits: {
    maxStoriesPerMonth: number;   // 每月最大故事数
    maxPagesPerStory: number;     // 每个故事最大页数
    maxCustomCharacters: number;  // 最大自定义角色数
  };
  
  // 特殊权限
  permissions: {
    commercialUse: boolean;       // 商业使用权
    apiAccess: boolean;           // API访问权
    exportFormats: string[];      // 导出格式
    prioritySupport: boolean;     // 优先客服支持
  };
}

// 不同订阅计划的功能配置
const SUBSCRIPTION_FEATURES: Record<SubscriptionPlanType, SubscriptionFeatures> = {
  // 免费计划
  free: {
    aiModel: {
      model: 'gemini-2.5-flash',
      temperature: 0.7,
      topK: 20,
      maxOutputTokens: 8192
    },
    imageGeneration: {
      model: 'imagen-3.0-generate-001',
      resolution: '512x512',
      quality: 'standard',
      maxRetries: 1,
      enhancedPrompt: false
    },
    audioGeneration: {
      model: 'gemini-2.5-flash-preview-tts',
      quality: 'standard',
      voiceOptions: ['gentle_female', 'warm_male']
    },
    limits: {
      maxStoriesPerMonth: 5,
      maxPagesPerStory: 8,
      maxCustomCharacters: 0
    },
    permissions: {
      commercialUse: false,
      apiAccess: false,
      exportFormats: ['pdf'],
      prioritySupport: false
    }
  },
  
  // 基础会员
  basic_monthly: {
    aiModel: {
      model: 'gemini-2.5-flash',
      temperature: 0.75,
      topK: 30,
      maxOutputTokens: 6144
    },
    imageGeneration: {
      model: 'imagen-3.0-generate-001',
      resolution: '768x768',
      quality: 'standard',
      maxRetries: 2,
      enhancedPrompt: false
    },
    audioGeneration: {
      model: 'gemini-2.5-flash-preview-tts',
      quality: 'standard',
      voiceOptions: ['gentle_female', 'warm_male', 'child_friendly']
    },
    limits: {
      maxStoriesPerMonth: 50,
      maxPagesPerStory: 10,
      maxCustomCharacters: 2
    },
    permissions: {
      commercialUse: false,
      apiAccess: false,
      exportFormats: ['pdf', 'epub'],
      prioritySupport: false
    }
  },
  
  // 专业会员
  pro_monthly: {
    aiModel: {
      model: 'gemini-2.5-pro',
      temperature: 0.85,
      topK: 40,
      maxOutputTokens: 8192
    },
    imageGeneration: {
      model: 'imagen-3.0-generate-002',
      resolution: '1024x1024',
      quality: 'premium',
      maxRetries: 3,
      enhancedPrompt: true
    },
    audioGeneration: {
      model: 'gemini-2.5-pro-preview-tts',
      quality: 'high',
      voiceOptions: ['gentle_female', 'warm_male', 'child_friendly', 'storyteller']
    },
    limits: {
      maxStoriesPerMonth: 200,
      maxPagesPerStory: 15,
      maxCustomCharacters: 5
    },
    permissions: {
      commercialUse: false,
      apiAccess: false,
      exportFormats: ['pdf', 'epub', 'mp3'],
      prioritySupport: true
    }
  },
  
  // 无限会员
  unlimited_monthly: {
    aiModel: {
      model: 'gemini-2.5-ultra',
      temperature: 0.9,
      topK: 50,
      maxOutputTokens: 16384
    },
    imageGeneration: {
      model: 'imagen-3.0-generate-003',
      resolution: '2048x2048',
      quality: 'ultra',
      maxRetries: 5,
      enhancedPrompt: true
    },
    audioGeneration: {
      model: 'gemini-2.5-ultra-preview-tts',
      quality: 'premium',
      voiceOptions: ['gentle_female', 'warm_male', 'child_friendly', 'storyteller', 'dramatic', 'whispering']
    },
    limits: {
      maxStoriesPerMonth: 999999, // 实质上无限制
      maxPagesPerStory: 30,
      maxCustomCharacters: 20
    },
    permissions: {
      commercialUse: true,
      apiAccess: true,
      exportFormats: ['pdf', 'epub', 'mp3', 'docx', 'json'],
      prioritySupport: true
    }
  },
  
  // 专业会员年付（功能同专业会员月付）
  pro_yearly: {
    aiModel: {
      model: 'gemini-2.5-pro',
      temperature: 0.85,
      topK: 40,
      maxOutputTokens: 8192
    },
    imageGeneration: {
      model: 'imagen-3.0-generate-002',
      resolution: '1024x1024',
      quality: 'premium',
      maxRetries: 3,
      enhancedPrompt: true
    },
    audioGeneration: {
      model: 'gemini-2.5-pro-preview-tts',
      quality: 'high',
      voiceOptions: ['gentle_female', 'warm_male', 'child_friendly', 'storyteller']
    },
    limits: {
      maxStoriesPerMonth: 200,
      maxPagesPerStory: 15,
      maxCustomCharacters: 5
    },
    permissions: {
      commercialUse: false,
      apiAccess: false,
      exportFormats: ['pdf', 'epub', 'mp3'],
      prioritySupport: true
    }
  }
};

export class SubscriptionService {
  /**
   * 获取用户的订阅功能配置
   */
  static getFeatures(subscription?: UserSubscription | null): SubscriptionFeatures {
    // 如果没有订阅或订阅不活跃，返回免费计划
    if (!subscription || subscription.status !== 'active') {
      return SUBSCRIPTION_FEATURES.free;
    }
    
    // 根据订阅计划返回相应的功能配置
    const plan = subscription.plan as SubscriptionPlanType;
    return SUBSCRIPTION_FEATURES[plan] || SUBSCRIPTION_FEATURES.free;
  }
  
  /**
   * 检查用户是否有权限使用特定功能
   */
  static hasPermission(subscription: UserSubscription | null, permission: keyof SubscriptionFeatures['permissions']): boolean {
    const features = this.getFeatures(subscription);
    return features.permissions[permission] || false;
  }
  
  /**
   * 检查用户是否超出了使用限制
   */
  static checkLimit(subscription: UserSubscription | null, limit: keyof SubscriptionFeatures['limits'], currentValue: number): boolean {
    const features = this.getFeatures(subscription);
    return currentValue < features.limits[limit];
  }
  
  /**
   * 获取用户的AI模型配置
   */
  static getAIModelConfig(subscription: UserSubscription | null): SubscriptionFeatures['aiModel'] {
    return this.getFeatures(subscription).aiModel;
  }
  
  /**
   * 获取用户的图片生成配置
   */
  static getImageGenerationConfig(subscription: UserSubscription | null): SubscriptionFeatures['imageGeneration'] {
    return this.getFeatures(subscription).imageGeneration;
  }
  
  /**
   * 获取用户的音频生成配置
   */
  static getAudioGenerationConfig(subscription: UserSubscription | null): SubscriptionFeatures['audioGeneration'] {
    return this.getFeatures(subscription).audioGeneration;
  }
  
  /**
   * 获取用户可用的声音选项
   */
  static getAvailableVoices(subscription: UserSubscription | null): string[] {
    return this.getFeatures(subscription).audioGeneration.voiceOptions;
  }
  
  /**
   * 获取用户可用的导出格式
   */
  static getAvailableExportFormats(subscription: UserSubscription | null): string[] {
    return this.getFeatures(subscription).permissions.exportFormats;
  }
  
  /**
   * 检查用户是否有商业使用权限
   */
  static hasCommercialUsePermission(subscription: UserSubscription | null): boolean {
    return this.hasPermission(subscription, 'commercialUse');
  }
  
  /**
   * 检查用户是否有API访问权限
   */
  static hasAPIAccess(subscription: UserSubscription | null): boolean {
    return this.hasPermission(subscription, 'apiAccess');
  }
  
  /**
   * 检查用户是否有优先客服支持
   */
  static hasPrioritySupport(subscription: UserSubscription | null): boolean {
    return this.hasPermission(subscription, 'prioritySupport');
  }
}