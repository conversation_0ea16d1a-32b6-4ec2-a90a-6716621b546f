// @ts-nocheck
/**
 * 认证中间件
 */

import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { ApiResponse, ErrorCodes } from '../types/api';
import type { Env } from '../types/hono';

export async function authMiddleware(c: Context<{ Bindings: Env }>, next: Next) {
  try {
    // 首先检查是否有Authorization头
    const authHeader = c.req.header('Authorization');

    // 如果有Authorization头，优先使用JWT验证
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);

      try {
        const payload = await verify(token, c.env.JWT_SECRET);

        if (payload && payload.type === 'access') {
          // 检查令牌是否过期
          const now = Math.floor(Date.now() / 1000);
          if (!payload.exp || payload.exp >= now) {
            // 🔍 ENHANCED: 验证用户是否存在于数据库中
            try {
              const { StorageService } = await import('../services/storage');
              const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
              const dbUser = await storageService.getUserById(payload.userId as string);

              if (!dbUser) {
                console.error(`🚨 JWT中的用户ID ${payload.userId} 在数据库中不存在`);
                return c.json({
                  success: false,
                  error: '用户不存在',
                  code: 'USER_NOT_FOUND'
                }, 401);
              }

              console.log(`✅ 用户认证成功: ${dbUser.email} (${dbUser.id})`);

              // 将完整的用户信息添加到上下文
              c.set('user', dbUser);
              await next();
              return;

            } catch (dbError) {
              console.error('🚨 验证用户存在性时出错:', dbError);
              return c.json({
                success: false,
                error: '认证验证失败',
                code: 'AUTH_VERIFICATION_FAILED'
              }, 500);
            }
          } else {
            console.warn(`🚨 JWT令牌已过期: exp=${payload.exp}, now=${now}`);
          }
        }
      } catch (jwtError) {
        console.warn('JWT验证失败:', jwtError);
      }
    }

    // 🔒 CRITICAL SECURITY: 生产环境只允许有效JWT认证
    if (c.env.ENVIRONMENT === 'production') {
      // 生产环境：只有JWT验证失败才返回401
      return c.json<ApiResponse>({
        success: false,
        error: '未提供有效的认证令牌',
        code: ErrorCodes.UNAUTHORIZED
      }, 401);
    }

    // 🔒 开发环境：仅在明确启用调试模式时使用
    const isDebugMode = c.env.ENVIRONMENT === 'development' && c.env.DEBUG_MODE === 'true';

    if (isDebugMode) {
      console.log('使用调试模式认证');
      const debugUserId = 'debug-user';
      try {
        const existingUser = await c.env.DB.prepare(
          'SELECT id FROM users WHERE id = ?'
        ).bind(debugUserId).first();

        if (!existingUser) {
          await c.env.DB.prepare(`
            INSERT INTO users (id, email, name, google_id, credits, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `).bind(
            debugUserId,
            '<EMAIL>',
            '调试用户',
            'debug-google-id',
            100,
            new Date().toISOString(),
            new Date().toISOString()
          ).run();
        }
      } catch (dbError) {
        console.warn('创建调试用户失败:', dbError);
      }

      c.set('user', {
        id: debugUserId,
        email: '<EMAIL>',
        name: '调试用户',
        credits: 100
      } as any);
      await next();
      return;
    }

    // 如果既没有有效的JWT也不在调试模式，返回401错误
    return c.json<ApiResponse>({
      success: false,
      error: '未提供有效的认证令牌',
      code: ErrorCodes.UNAUTHORIZED
    }, 401);
  } catch (error) {
    console.error('Auth middleware error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '认证失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
}