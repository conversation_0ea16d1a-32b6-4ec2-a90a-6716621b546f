#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/wrangler@3.114.10_@cloudflare+workers-types@4.20250701.0/node_modules/wrangler/bin/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/wrangler@3.114.10_@cloudflare+workers-types@4.20250701.0/node_modules/wrangler/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/wrangler@3.114.10_@cloudflare+workers-types@4.20250701.0/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/wrangler@3.114.10_@cloudflare+workers-types@4.20250701.0/node_modules/wrangler/bin/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/wrangler@3.114.10_@cloudflare+workers-types@4.20250701.0/node_modules/wrangler/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/wrangler@3.114.10_@cloudflare+workers-types@4.20250701.0/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../wrangler/bin/wrangler.js" "$@"
else
  exec node  "$basedir/../wrangler/bin/wrangler.js" "$@"
fi
