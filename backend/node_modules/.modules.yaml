hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.7':
    '@babel/compat-data': private
  '@babel/core@7.27.7':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.7)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.7':
    '@babel/traverse': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cloudflare/kv-asset-handler@0.3.4':
    '@cloudflare/kv-asset-handler': private
  '@cloudflare/unenv-preset@2.0.2(unenv@2.0.0-rc.14)(workerd@1.20250408.0)':
    '@cloudflare/unenv-preset': private
  '@cloudflare/workerd-darwin-64@1.20250408.0':
    '@cloudflare/workerd-darwin-64': private
  '@cloudflare/workerd-darwin-arm64@1.20250408.0':
    '@cloudflare/workerd-darwin-arm64': private
  '@cloudflare/workerd-linux-64@1.20250408.0':
    '@cloudflare/workerd-linux-64': private
  '@cloudflare/workerd-linux-arm64@1.20250408.0':
    '@cloudflare/workerd-linux-arm64': private
  '@cloudflare/workerd-windows-64@1.20250408.0':
    '@cloudflare/workerd-windows-64': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild-plugins/node-globals-polyfill@0.2.3(esbuild@0.17.19)':
    '@esbuild-plugins/node-globals-polyfill': private
  '@esbuild-plugins/node-modules-polyfill@0.2.2(esbuild@0.17.19)':
    '@esbuild-plugins/node-modules-polyfill': private
  '@esbuild/android-arm64@0.17.19':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.17.19':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.17.19':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.17.19':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.17.19':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.17.19':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.17.19':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.17.19':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.17.19':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.17.19':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.17.19':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.17.19':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.17.19':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.17.19':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.17.19':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.17.19':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.17.19':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.17.19':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.17.19':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.17.19':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.17.19':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.17.19':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.30.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.30.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@fastify/busboy@2.1.1':
    '@fastify/busboy': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.11':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.3':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/project-service@8.35.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.35.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.35.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.35.1(eslint@9.30.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.35.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.35.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.35.1(eslint@9.30.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.35.1':
    '@typescript-eslint/visitor-keys': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.2:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  as-table@1.0.55:
    as-table: private
  babel-jest@29.7.0(@babel/core@7.27.7):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.7):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.27.7):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bignumber.js@9.3.0:
    bignumber.js: private
  blake3-wasm@2.1.5:
    blake3-wasm: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  cliui@8.0.1:
    cliui: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@0.7.2:
    cookie: private
  create-jest@29.7.0(@types/node@22.15.34):
    create-jest: private
  cross-spawn@7.0.6:
    cross-spawn: private
  data-uri-to-buffer@2.0.2:
    data-uri-to-buffer: private
  debug@4.4.1:
    debug: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  defu@6.1.4:
    defu: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  diff-sequences@29.6.3:
    diff-sequences: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  electron-to-chromium@1.5.178:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.17.19:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@0.6.1:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  execa@5.1.1:
    execa: private
  exit-hook@2.2.1:
    exit-hook: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  exsolve@1.0.7:
    exsolve: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gaxios@6.7.1:
    gaxios: private
  gcp-metadata@6.1.1:
    gcp-metadata: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-source@2.0.12:
    get-source: private
  get-stream@6.0.1:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.2.3:
    glob: private
  globals@14.0.0:
    globals: private
  google-auth-library@9.15.1:
    google-auth-library: private
  google-logging-utils@0.0.2:
    google-logging-utils: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gtoken@7.1.0:
    gtoken: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  html-escaper@2.0.2:
    html-escaper: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  ignore@7.0.5:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-stream@2.0.1:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@22.15.34):
    jest-cli: private
  jest-config@29.7.0(@types/node@22.15.34):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-bigint@1.0.0:
    json-bigint: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jwa@2.0.1:
    jwa: private
  jws@4.0.0:
    jws: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.25.9:
    magic-string: private
  make-dir@4.0.0:
    make-dir: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime@3.0.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  miniflare@3.20250408.2:
    miniflare: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  mustache@4.2.0:
    mustache: private
  natural-compare@1.4.0:
    natural-compare: private
  node-fetch@2.7.0:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  object-inspect@1.13.4:
    object-inspect: private
  ohash@2.0.11:
    ohash: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pathe@2.0.3:
    pathe: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@29.7.0:
    pretty-format: private
  printable-characters@1.0.42:
    printable-characters: private
  prompts@2.4.2:
    prompts: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.14.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@18.3.1:
    react-is: private
  require-directory@2.1.1:
    require-directory: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rollup-plugin-inject@3.0.2:
    rollup-plugin-inject: private
  rollup-plugin-node-polyfills@0.2.1:
    rollup-plugin-node-polyfills: private
  rollup-pluginutils@2.8.2:
    rollup-pluginutils: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  semver@7.7.2:
    semver: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  source-map-support@0.5.13:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  stacktracey@2.1.8:
    stacktracey: private
  stoppable@1.1.0:
    stoppable: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  test-exclude@6.0.0:
    test-exclude: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.21.3:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  undici-types@6.21.0:
    undici-types: private
  undici@5.29.0:
    undici: private
  unenv@2.0.0-rc.14:
    unenv: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  uuid@9.0.1:
    uuid: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  walker@1.0.8:
    walker: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  workerd@1.20250408.0:
    workerd: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.0:
    ws: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  youch@3.3.4:
    youch: private
  zod-to-json-schema@3.24.6(zod@3.25.75):
    zod-to-json-schema: private
  zod@3.25.75:
    zod: private
ignoredBuilds:
  - sharp
  - workerd
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.4.1
pendingBuilds: []
prunedAt: Tue, 08 Jul 2025 03:44:24 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@cloudflare/workerd-darwin-64@1.20250408.0'
  - '@cloudflare/workerd-linux-64@1.20250408.0'
  - '@cloudflare/workerd-linux-arm64@1.20250408.0'
  - '@cloudflare/workerd-windows-64@1.20250408.0'
  - '@emnapi/runtime@1.4.3'
  - '@esbuild/android-arm64@0.17.19'
  - '@esbuild/android-arm@0.17.19'
  - '@esbuild/android-x64@0.17.19'
  - '@esbuild/darwin-x64@0.17.19'
  - '@esbuild/freebsd-arm64@0.17.19'
  - '@esbuild/freebsd-x64@0.17.19'
  - '@esbuild/linux-arm64@0.17.19'
  - '@esbuild/linux-arm@0.17.19'
  - '@esbuild/linux-ia32@0.17.19'
  - '@esbuild/linux-loong64@0.17.19'
  - '@esbuild/linux-mips64el@0.17.19'
  - '@esbuild/linux-ppc64@0.17.19'
  - '@esbuild/linux-riscv64@0.17.19'
  - '@esbuild/linux-s390x@0.17.19'
  - '@esbuild/linux-x64@0.17.19'
  - '@esbuild/netbsd-x64@0.17.19'
  - '@esbuild/openbsd-x64@0.17.19'
  - '@esbuild/sunos-x64@0.17.19'
  - '@esbuild/win32-arm64@0.17.19'
  - '@esbuild/win32-ia32@0.17.19'
  - '@esbuild/win32-x64@0.17.19'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-x64@0.33.5'
  - tslib@2.8.1
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
