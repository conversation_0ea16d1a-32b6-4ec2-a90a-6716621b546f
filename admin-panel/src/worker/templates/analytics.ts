import { getCommonStyles, getNavigation, getCommonScripts } from './common';

export function getAnalyticsHTML() {
  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>数据分析 - StoryWeaver Admin</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
          ${getCommonStyles()}
          .analytics-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
          }
          .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
          }
          .filter-group label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #666;
          }
          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }
          .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
          }
          .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
          }
          .metric-label {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
          }
          .metric-change {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 4px;
          }
          .metric-change.positive {
            background: #d1fae5;
            color: #065f46;
          }
          .metric-change.negative {
            background: #fee2e2;
            color: #991b1b;
          }
          .chart-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
          }
          .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
          }
          .chart-tabs {
            display: flex;
            gap: 10px;
          }
          .chart-tab {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
          }
          .chart-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
          }
        </style>
      </head>
      <body>
        ${getNavigation('analytics')}
        
        <div class="main-content">
          <div class="page-header">
            <h1>📈 数据分析</h1>
            <p>深入了解平台数据和用户行为</p>
          </div>

          <!-- 筛选器 -->
          <div class="analytics-filters">
            <div class="filter-group">
              <label>时间范围</label>
              <select id="timeRange" class="form-select" style="width: 150px;">
                <option value="7d">最近7天</option>
                <option value="30d" selected>最近30天</option>
                <option value="90d">最近90天</option>
                <option value="1y">最近1年</option>
              </select>
            </div>
            <div class="filter-group">
              <label>数据类型</label>
              <select id="dataType" class="form-select" style="width: 150px;">
                <option value="all" selected>全部数据</option>
                <option value="users">用户数据</option>
                <option value="stories">故事数据</option>
                <option value="revenue">收入数据</option>
              </select>
            </div>
            <button class="btn btn-primary" onclick="refreshData()">
              🔄 刷新数据
            </button>
            <button class="btn btn-secondary" onclick="exportData()">
              📊 导出报告
            </button>
          </div>

          <!-- 关键指标 -->
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-value" style="color: #3b82f6;" id="totalUsers">-</div>
              <div class="metric-label">总用户数</div>
              <div class="metric-change positive" id="userChange">+12.5%</div>
            </div>
            <div class="metric-card">
              <div class="metric-value" style="color: #10b981;" id="activeUsers">-</div>
              <div class="metric-label">活跃用户</div>
              <div class="metric-change positive" id="activeChange">+8.3%</div>
            </div>
            <div class="metric-card">
              <div class="metric-value" style="color: #f59e0b;" id="totalStories">-</div>
              <div class="metric-label">故事总数</div>
              <div class="metric-change positive" id="storyChange">+15.7%</div>
            </div>
            <div class="metric-card">
              <div class="metric-value" style="color: #ef4444;" id="conversionRate">-</div>
              <div class="metric-label">转化率</div>
              <div class="metric-change negative" id="conversionChange">-2.1%</div>
            </div>
            <div class="metric-card">
              <div class="metric-value" style="color: #8b5cf6;" id="totalRevenue">-</div>
              <div class="metric-label">总收入 (¥)</div>
              <div class="metric-change positive" id="revenueChange">+23.4%</div>
            </div>
            <div class="metric-card">
              <div class="metric-value" style="color: #06b6d4;" id="avgSession">-</div>
              <div class="metric-label">平均会话时长</div>
              <div class="metric-change positive" id="sessionChange">+5.2%</div>
            </div>
          </div>

          <!-- 用户增长分析 -->
          <div class="chart-section">
            <div class="chart-header">
              <h3>用户增长分析</h3>
              <div class="chart-tabs">
                <div class="chart-tab active" onclick="switchUserChart('daily')">日增长</div>
                <div class="chart-tab" onclick="switchUserChart('weekly')">周增长</div>
                <div class="chart-tab" onclick="switchUserChart('monthly')">月增长</div>
              </div>
            </div>
            <canvas id="userGrowthChart" height="100"></canvas>
          </div>

          <!-- 收入分析 -->
          <div class="chart-section">
            <div class="chart-header">
              <h3>收入分析</h3>
              <div class="chart-tabs">
                <div class="chart-tab active" onclick="switchRevenueChart('timeline')">时间趋势</div>
                <div class="chart-tab" onclick="switchRevenueChart('plans')">订阅计划</div>
                <div class="chart-tab" onclick="switchRevenueChart('regions')">地区分布</div>
              </div>
            </div>
            <canvas id="revenueChart" height="100"></canvas>
          </div>

          <!-- 故事分析 -->
          <div class="chart-section">
            <div class="chart-header">
              <h3>故事数据分析</h3>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
              <div>
                <h4 style="margin-bottom: 15px;">故事状态分布</h4>
                <canvas id="storyStatusChart" height="200"></canvas>
              </div>
              <div>
                <h4 style="margin-bottom: 15px;">语言分布</h4>
                <canvas id="storyLanguageChart" height="200"></canvas>
              </div>
            </div>
          </div>

          <!-- 用户行为分析 -->
          <div class="chart-section">
            <div class="chart-header">
              <h3>用户行为分析</h3>
            </div>
            <canvas id="userBehaviorChart" height="100"></canvas>
          </div>
        </div>

        <script>
          ${getCommonScripts()}
          
          let currentUserChart = 'daily';
          let currentRevenueChart = 'timeline';
          let charts = {};
          
          // 加载分析数据
          async function loadAnalyticsData() {
            try {
              showLoading();
              
              // 模拟API调用
              const data = await simulateAnalyticsAPI();
              
              updateMetrics(data.metrics);
              createCharts(data.charts);
              hideLoading();
              
            } catch (error) {
              console.error('加载分析数据失败:', error);
              showMessage('加载数据失败: ' + error.message, 'error');
              hideLoading();
            }
          }
          
          function updateMetrics(metrics) {
            document.getElementById('totalUsers').textContent = metrics.totalUsers.toLocaleString();
            document.getElementById('activeUsers').textContent = metrics.activeUsers.toLocaleString();
            document.getElementById('totalStories').textContent = metrics.totalStories.toLocaleString();
            document.getElementById('conversionRate').textContent = metrics.conversionRate + '%';
            document.getElementById('totalRevenue').textContent = '¥' + metrics.totalRevenue.toLocaleString();
            document.getElementById('avgSession').textContent = metrics.avgSession + 'm';
          }
          
          function createCharts(data) {
            // 用户增长图表
            const userCtx = document.getElementById('userGrowthChart').getContext('2d');
            charts.userGrowth = new Chart(userCtx, {
              type: 'line',
              data: {
                labels: data.userGrowth.daily.labels,
                datasets: [{
                  label: '新增用户',
                  data: data.userGrowth.daily.newUsers,
                  borderColor: '#3b82f6',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  tension: 0.4
                }, {
                  label: '活跃用户',
                  data: data.userGrowth.daily.activeUsers,
                  borderColor: '#10b981',
                  backgroundColor: 'rgba(16, 185, 129, 0.1)',
                  tension: 0.4
                }]
              },
              options: {
                responsive: true,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }
            });
            
            // 收入图表
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            charts.revenue = new Chart(revenueCtx, {
              type: 'bar',
              data: {
                labels: data.revenue.timeline.labels,
                datasets: [{
                  label: '收入 (¥)',
                  data: data.revenue.timeline.values,
                  backgroundColor: '#8b5cf6'
                }]
              },
              options: {
                responsive: true,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }
            });
            
            // 故事状态图表
            const storyStatusCtx = document.getElementById('storyStatusChart').getContext('2d');
            charts.storyStatus = new Chart(storyStatusCtx, {
              type: 'doughnut',
              data: {
                labels: ['已完成', '草稿', '生成中', '失败'],
                datasets: [{
                  data: [3456, 1234, 567, 123],
                  backgroundColor: ['#10b981', '#f59e0b', '#3b82f6', '#ef4444']
                }]
              },
              options: {
                responsive: true
              }
            });
            
            // 语言分布图表
            const languageCtx = document.getElementById('storyLanguageChart').getContext('2d');
            charts.language = new Chart(languageCtx, {
              type: 'pie',
              data: {
                labels: ['中文', '英文', '日文', '其他'],
                datasets: [{
                  data: [4567, 3456, 789, 234],
                  backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
                }]
              },
              options: {
                responsive: true
              }
            });
            
            // 用户行为图表
            const behaviorCtx = document.getElementById('userBehaviorChart').getContext('2d');
            charts.behavior = new Chart(behaviorCtx, {
              type: 'radar',
              data: {
                labels: ['故事创建', '分享行为', '付费转化', '活跃度', '留存率', '满意度'],
                datasets: [{
                  label: '当前周期',
                  data: [85, 72, 68, 91, 76, 88],
                  borderColor: '#667eea',
                  backgroundColor: 'rgba(102, 126, 234, 0.2)'
                }, {
                  label: '上个周期',
                  data: [78, 68, 65, 87, 72, 85],
                  borderColor: '#f59e0b',
                  backgroundColor: 'rgba(245, 158, 11, 0.2)'
                }]
              },
              options: {
                responsive: true,
                scales: {
                  r: {
                    beginAtZero: true,
                    max: 100
                  }
                }
              }
            });
          }
          
          function switchUserChart(type) {
            // 更新标签页样式
            document.querySelectorAll('.chart-tabs .chart-tab').forEach(tab => {
              tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            currentUserChart = type;
            // 这里可以更新图表数据
            showMessage('切换到' + type + '视图', 'success');
          }
          
          function switchRevenueChart(type) {
            // 更新标签页样式
            document.querySelectorAll('.chart-tabs .chart-tab').forEach(tab => {
              tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            currentRevenueChart = type;
            showMessage('切换到' + type + '视图', 'success');
          }
          
          function refreshData() {
            showMessage('正在刷新数据...', 'info');
            loadAnalyticsData();
          }
          
          function exportData() {
            showMessage('正在生成报告...', 'info');
            // 模拟导出
            setTimeout(() => {
              showMessage('报告已生成并下载', 'success');
            }, 2000);
          }
          
          // 模拟API数据
          function simulateAnalyticsAPI() {
            return new Promise(resolve => {
              setTimeout(() => {
                resolve({
                  metrics: {
                    totalUsers: 12543,
                    activeUsers: 8765,
                    totalStories: 45678,
                    conversionRate: 12.5,
                    totalRevenue: 234567,
                    avgSession: 25
                  },
                  charts: {
                    userGrowth: {
                      daily: {
                        labels: ['1日', '2日', '3日', '4日', '5日', '6日', '7日'],
                        newUsers: [45, 52, 38, 67, 73, 58, 62],
                        activeUsers: [234, 267, 198, 345, 389, 298, 312]
                      }
                    },
                    revenue: {
                      timeline: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        values: [15000, 18000, 22000, 25000, 28000, 32000]
                      }
                    }
                  }
                });
              }, 1000);
            });
          }
          
          // 页面加载时获取数据
          document.addEventListener('DOMContentLoaded', loadAnalyticsData);
        </script>
      </body>
    </html>
  `;
}`;