export function getCommonStyles() {
  return `
    :root {
      --primary-color: #667eea;
      --secondary-color: #764ba2;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --text-color: #1f2937;
      --text-muted: #6b7280;
      --border-color: #e5e7eb;
      --bg-color: #f9fafb;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: var(--bg-color);
      color: var(--text-color);
      line-height: 1.6;
    }
    
    .container {
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 250px;
      background: white;
      box-shadow: 2px 0 4px rgba(0,0,0,0.1);
      padding: 20px 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 0 20px 20px;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
    }
    
    .sidebar-header h2 {
      color: var(--primary-color);
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .nav-menu {
      list-style: none;
    }
    
    .nav-item {
      margin-bottom: 5px;
    }
    
    .nav-link {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: var(--text-muted);
      text-decoration: none;
      transition: all 0.2s;
    }
    
    .nav-link:hover,
    .nav-link.active {
      background: var(--primary-color);
      color: white;
    }
    
    .nav-link .icon {
      margin-right: 10px;
      font-size: 1.1rem;
    }
    
    .main-content {
      margin-left: 250px;
      padding: 30px;
      width: calc(100% - 250px);
    }
    
    .page-header {
      margin-bottom: 30px;
    }
    
    .page-header h1 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 5px;
    }
    
    .page-header p {
      color: var(--text-muted);
    }
    
    .btn {
      display: inline-flex;
      align-items: center;
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      transition: all 0.2s;
    }
    
    .btn-primary {
      background: var(--primary-color);
      color: white;
    }
    
    .btn-primary:hover {
      background: #5a6fd8;
    }
    
    .btn-secondary {
      background: #f3f4f6;
      color: var(--text-color);
      border: 1px solid var(--border-color);
    }
    
    .btn-secondary:hover {
      background: #e5e7eb;
    }
    
    .btn-success {
      background: var(--success-color);
      color: white;
    }
    
    .btn-warning {
      background: var(--warning-color);
      color: white;
    }
    
    .btn-danger {
      background: var(--error-color);
      color: white;
    }
    
    .card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .table th,
    .table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }
    
    .table th {
      background: #f8fafc;
      font-weight: 600;
      color: var(--text-color);
    }
    
    .table tr:hover {
      background: #f8fafc;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .form-input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      font-size: 14px;
      background: white;
    }
    
    .form-checkbox {
      margin-right: 8px;
    }
    
    .alert {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 20px;
    }
    
    .alert-success {
      background: #d1fae5;
      color: #065f46;
      border: 1px solid #a7f3d0;
    }
    
    .alert-error {
      background: #fee2e2;
      color: #991b1b;
      border: 1px solid #fecaca;
    }
    
    .alert-warning {
      background: #fef3c7;
      color: #92400e;
      border: 1px solid #fde68a;
    }
    
    .loading {
      display: none;
      text-align: center;
      padding: 40px;
    }
    
    .loading.show {
      display: block;
    }
    
    .spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
    }
    
    .modal.show {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .modal-content {
      background: white;
      border-radius: 8px;
      padding: 30px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    .modal-header {
      margin-bottom: 20px;
    }
    
    .modal-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 5px;
    }
    
    .modal-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 20px;
    }
    
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      .main-content {
        margin-left: 0;
        width: 100%;
        padding: 20px;
      }
    }
  `;
}

export function getNavigation(activePage: string) {
  return `
    <div class="container">
      <nav class="sidebar">
        <div class="sidebar-header">
          <h2>🎯 StoryWeaver Admin</h2>
        </div>
        <ul class="nav-menu">
          <li class="nav-item">
            <a href="/dashboard" class="nav-link ${activePage === 'dashboard' ? 'active' : ''}">
              <span class="icon">📊</span>
              仪表板
            </a>
          </li>
          <li class="nav-item">
            <a href="/analytics" class="nav-link ${activePage === 'analytics' ? 'active' : ''}">
              <span class="icon">📈</span>
              数据分析
            </a>
          </li>
          <li class="nav-item">
            <a href="/users" class="nav-link ${activePage === 'users' ? 'active' : ''}">
              <span class="icon">👥</span>
              用户管理
            </a>
          </li>
          <li class="nav-item">
            <a href="/stories" class="nav-link ${activePage === 'stories' ? 'active' : ''}">
              <span class="icon">📚</span>
              故事管理
            </a>
          </li>
          <li class="nav-item">
            <a href="/settings" class="nav-link ${activePage === 'settings' ? 'active' : ''}">
              <span class="icon">⚙️</span>
              系统设置
            </a>
          </li>
          <li class="nav-item">
            <a href="/" class="nav-link">
              <span class="icon">🚪</span>
              退出登录
            </a>
          </li>
        </ul>
      </nav>
  `;
}

export function getCommonScripts() {
  return `
    function showMessage(message, type = 'info') {
      const alertClass = type === 'error' ? 'alert-error' : 
                        type === 'success' ? 'alert-success' : 
                        type === 'warning' ? 'alert-warning' : 'alert-info';
      
      const alertDiv = document.createElement('div');
      alertDiv.className = 'alert ' + alertClass;
      alertDiv.textContent = message;
      
      const container = document.querySelector('.main-content');
      container.insertBefore(alertDiv, container.firstChild);
      
      setTimeout(() => {
        alertDiv.remove();
      }, 5000);
    }
    
    function showLoading() {
      let loading = document.querySelector('.loading');
      if (!loading) {
        loading = document.createElement('div');
        loading.className = 'loading';
        loading.innerHTML = '<div class="spinner"></div><p>加载中...</p>';
        document.querySelector('.main-content').appendChild(loading);
      }
      loading.classList.add('show');
    }
    
    function hideLoading() {
      const loading = document.querySelector('.loading');
      if (loading) {
        loading.classList.remove('show');
      }
    }
    
    function showModal(title, content, actions = []) {
      const modal = document.createElement('div');
      modal.className = 'modal show';
      modal.innerHTML = \`
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title">\${title}</h3>
          </div>
          <div class="modal-body">
            \${content}
          </div>
          <div class="modal-actions">
            \${actions.map(action => \`<button class="btn \${action.class}" onclick="\${action.onclick}">\${action.text}</button>\`).join('')}
            <button class="btn btn-secondary" onclick="closeModal()">取消</button>
          </div>
        </div>
      \`;
      
      document.body.appendChild(modal);
      
      // 点击背景关闭
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          closeModal();
        }
      });
    }
    
    function closeModal() {
      const modal = document.querySelector('.modal');
      if (modal) {
        modal.remove();
      }
    }
    
    // 检查登录状态
    function checkAuth() {
      const token = localStorage.getItem('admin-token');
      if (!token) {
        window.location.href = '/';
        return false;
      }
      return true;
    }
    
    // 页面加载时检查认证
    document.addEventListener('DOMContentLoaded', () => {
      if (window.location.pathname !== '/') {
        checkAuth();
      }
    });
  `;
}`;