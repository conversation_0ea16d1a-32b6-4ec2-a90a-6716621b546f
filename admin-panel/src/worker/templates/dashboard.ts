export function getDashboardHTML() {
  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>仪表板 - StoryWeaver Admin</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
          ${getCommonStyles()}
          .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }
          .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
          }
          .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
          }
          .stat-label {
            color: #666;
            font-size: 0.9rem;
          }
          .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
          }
          .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
          }
          @media (max-width: 768px) {
            .chart-grid {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        ${getNavigation('dashboard')}
        
        <div class="main-content">
          <div class="page-header">
            <h1>📊 仪表板</h1>
            <p>系统概览和关键指标</p>
          </div>

          <!-- 统计卡片 -->
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-number" id="totalUsers">-</div>
              <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="totalStories">-</div>
              <div class="stat-label">总故事数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="totalRevenue">-</div>
              <div class="stat-label">总收入 (¥)</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="activeUsers">-</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="chart-grid">
            <div class="chart-container">
              <h3>用户增长趋势</h3>
              <canvas id="userGrowthChart" width="400" height="200"></canvas>
            </div>
            <div class="chart-container">
              <h3>收入统计</h3>
              <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
          </div>

          <div class="chart-container">
            <h3>故事状态分布</h3>
            <canvas id="storyStatusChart" width="400" height="200"></canvas>
          </div>
        </div>

        <script>
          ${getCommonScripts()}
          
          // 加载仪表板数据
          async function loadDashboardData() {
            try {
              showLoading();
              
              // 模拟数据 - 在实际应用中这里会调用API
              const data = {
                totalUsers: 1234,
                totalStories: 5678,
                totalRevenue: 89234.50,
                activeUsers: 456,
                userGrowth: [
                  { date: '2024-01', users: 100 },
                  { date: '2024-02', users: 150 },
                  { date: '2024-03', users: 200 },
                  { date: '2024-04', users: 280 },
                  { date: '2024-05', users: 350 },
                  { date: '2024-06', users: 420 }
                ],
                revenue: [
                  { month: '1月', amount: 5000 },
                  { month: '2月', amount: 7500 },
                  { month: '3月', amount: 12000 },
                  { month: '4月', amount: 15000 },
                  { month: '5月', amount: 18000 },
                  { month: '6月', amount: 22000 }
                ],
                storyStatus: [
                  { status: '已完成', count: 3456 },
                  { status: '草稿', count: 1234 },
                  { status: '生成中', count: 567 },
                  { status: '失败', count: 123 }
                ]
              };
              
              updateStats(data);
              createCharts(data);
              hideLoading();
              
            } catch (error) {
              console.error('加载数据失败:', error);
              showMessage('加载数据失败: ' + error.message, 'error');
              hideLoading();
            }
          }
          
          function updateStats(data) {
            document.getElementById('totalUsers').textContent = data.totalUsers.toLocaleString();
            document.getElementById('totalStories').textContent = data.totalStories.toLocaleString();
            document.getElementById('totalRevenue').textContent = data.totalRevenue.toLocaleString();
            document.getElementById('activeUsers').textContent = data.activeUsers.toLocaleString();
          }
          
          function createCharts(data) {
            // 用户增长图表
            const userCtx = document.getElementById('userGrowthChart').getContext('2d');
            new Chart(userCtx, {
              type: 'line',
              data: {
                labels: data.userGrowth.map(item => item.date),
                datasets: [{
                  label: '用户数',
                  data: data.userGrowth.map(item => item.users),
                  borderColor: '#667eea',
                  backgroundColor: 'rgba(102, 126, 234, 0.1)',
                  tension: 0.4
                }]
              },
              options: {
                responsive: true,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }
            });
            
            // 收入图表
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
              type: 'bar',
              data: {
                labels: data.revenue.map(item => item.month),
                datasets: [{
                  label: '收入 (¥)',
                  data: data.revenue.map(item => item.amount),
                  backgroundColor: '#10b981'
                }]
              },
              options: {
                responsive: true,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }
            });
            
            // 故事状态图表
            const storyCtx = document.getElementById('storyStatusChart').getContext('2d');
            new Chart(storyCtx, {
              type: 'doughnut',
              data: {
                labels: data.storyStatus.map(item => item.status),
                datasets: [{
                  data: data.storyStatus.map(item => item.count),
                  backgroundColor: ['#10b981', '#f59e0b', '#3b82f6', '#ef4444']
                }]
              },
              options: {
                responsive: true
              }
            });
          }
          
          // 页面加载时获取数据
          document.addEventListener('DOMContentLoaded', loadDashboardData);
        </script>
      </body>
    </html>
  `;
}`;