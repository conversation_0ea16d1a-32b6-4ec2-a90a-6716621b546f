import { Hono } from 'hono';
import { cors } from 'hono/cors';

export interface Env {
  DB: D1Database;
  CACHE: KVNamespace;
  ASSETS: R2Bucket;
  JWT_SECRET: string;
  ADMIN_EMAIL: string;
  ADMIN_PASSWORD: string;
  ENVIRONMENT: string;
}

const app = new Hono<{ Bindings: Env }>();

// CORS 中间件
app.use('*', cors({
  origin: ['http://localhost:3001', 'https://storyweaver-admin-panel-dev.stawky.workers.dev'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// 健康检查
app.get('/api/health', (c) => {
  return c.json({ 
    success: true, 
    message: 'Admin Panel is running',
    env: c.env.ENVIRONMENT,
    timestamp: new Date().toISOString()
  });
});

// 简化的登录端点
app.post('/api/auth/login', async (c) => {
  try {
    const body = await c.req.text();
    console.log('Raw request body:', body);
    
    let email, password;
    try {
      const parsed = JSON.parse(body);
      email = parsed.email;
      password = parsed.password;
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      return c.json({
        success: false,
        error: 'Invalid JSON in request body',
        code: 'VALIDATION_ERROR'
      }, 400);
    }
    
    console.log('Login attempt:', { 
      email, 
      password: password ? '***' : 'missing',
      envEmail: c.env.ADMIN_EMAIL,
      envPassword: c.env.ADMIN_PASSWORD ? '***' : 'missing'
    });
    
    if (!email || !password) {
      return c.json({
        success: false,
        error: 'Email and password are required',
        code: 'VALIDATION_ERROR'
      }, 400);
    }
    
    if (email === c.env.ADMIN_EMAIL && password === c.env.ADMIN_PASSWORD) {
      console.log('Login successful');
      return c.json({
        success: true,
        data: {
          user: {
            id: 'admin-001',
            email: email,
            name: 'Administrator',
            role: 'admin'
          },
          token: 'simple-token-for-testing'
        },
        message: 'Login successful'
      });
    } else {
      console.log('Invalid credentials');
      return c.json({
        success: false,
        error: 'Invalid credentials',
        code: 'UNAUTHORIZED'
      }, 401);
    }
  } catch (error) {
    console.error('Login error:', error);
    return c.json({
      success: false,
      error: 'Login failed: ' + (error?.message || 'Unknown error'),
      code: 'INTERNAL_ERROR'
    }, 500);
  }
});

// 静态文件服务 - 简化版本，直接返回HTML
app.get('/', (c) => {
  return c.html(`
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/vite.svg" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>StoryWeaver Admin Panel</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
          body { 
            font-family: 'Inter', Arial, sans-serif; 
            margin: 0; 
            padding: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
          }
          h1 { color: #2c3e50; margin-bottom: 20px; }
          .login-form { margin-top: 30px; }
          .form-group { margin-bottom: 20px; text-align: left; }
          label { display: block; margin-bottom: 5px; font-weight: 500; color: #555; }
          input { 
            width: 100%; 
            padding: 12px; 
            border: 1px solid #ddd; 
            border-radius: 6px; 
            font-size: 14px;
            box-sizing: border-box;
          }
          button { 
            width: 100%; 
            padding: 12px; 
            background: #667eea; 
            color: white; 
            border: none; 
            border-radius: 6px; 
            font-size: 16px; 
            cursor: pointer;
            font-weight: 500;
          }
          button:hover { background: #5a6fd8; }
          .api-links { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; }
          .api-links a { color: #667eea; text-decoration: none; margin: 0 10px; }
          .message { margin-top: 15px; padding: 10px; border-radius: 4px; }
          .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
          .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🎯 StoryWeaver Admin Panel</h1>
          <p>管理后台登录</p>
          
          <div class="login-form">
            <div class="form-group">
              <label for="email">邮箱</label>
              <input type="email" id="email" value="<EMAIL>" />
            </div>
            <div class="form-group">
              <label for="password">密码</label>
              <input type="password" id="password" value="admin123" />
            </div>
            <button onclick="login()">登录</button>
            <div id="message"></div>
          </div>
          
          <div class="api-links">
            <p>API 端点测试:</p>
            <a href="/api/health" target="_blank">健康检查</a>
            <a href="#" onclick="testAPI()">测试API</a>
          </div>
        </div>
        
        <script>
          function showMessage(text, type = 'info') {
            const msg = document.getElementById('message');
            msg.className = 'message ' + type;
            msg.textContent = text;
          }
          
          async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
              showMessage('登录中...', 'info');
              const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
              });
              
              const data = await response.json();
              
              if (data.success) {
                showMessage('登录成功！', 'success');
                localStorage.setItem('admin-token', data.data.token);
                localStorage.setItem('admin-user', JSON.stringify(data.data.user));
                setTimeout(() => {
                  window.location.href = '/dashboard';
                }, 1000);
              } else {
                showMessage('登录失败: ' + data.error, 'error');
              }
            } catch (error) {
              showMessage('网络错误: ' + error.message, 'error');
            }
          }
          
          async function testAPI() {
            try {
              const response = await fetch('/api/health');
              const data = await response.json();
              alert('API测试成功:\\n' + JSON.stringify(data, null, 2));
            } catch (error) {
              alert('API测试失败: ' + error.message);
            }
          }
          
          // 检查是否已登录
          if (localStorage.getItem('admin-token')) {
            showMessage('检测到已登录状态', 'success');
          }
        </script>
      </body>
    </html>
  `);
});

// 仪表板页面
app.get('/dashboard', (c) => {
  return c.html(getDashboardHTML());
});

// 数据分析页面
app.get('/analytics', (c) => {
  return c.html(getAnalyticsHTML());
});

// 用户管理页面
app.get('/users', (c) => {
  return c.html(getUsersHTML());
});

// 系统设置页面
app.get('/settings', (c) => {
  return c.html(getSettingsHTML());
});

// 故事管理页面
app.get('/stories', (c) => {
  return c.html(getStoriesHTML());
});

// 其他路由重定向到首页
app.get('*', (c) => c.redirect('/'));

export default app;