// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`nopeResolver should return a single error from nopeResolver when validation fails 1`] = `
Object {
  "errors": Object {
    "birthYear": Object {
      "message": "The field is not a valid number",
      "ref": undefined,
    },
    "like": Object {
      "id": Object {
        "message": "The field is not a valid number",
        "ref": undefined,
      },
      "name": Object {
        "message": "This field is required",
        "ref": undefined,
      },
    },
    "password": Object {
      "message": "One uppercase character",
      "ref": Object {
        "name": "password",
      },
    },
    "repeatPassword": Object {
      "message": "This field is required",
      "ref": undefined,
    },
    "tags": Object {
      "message": "One or more elements are of invalid type",
      "ref": undefined,
    },
    "username": Object {
      "message": "This field is required",
      "ref": Object {
        "name": "username",
      },
    },
  },
  "values": Object {},
}
`;
