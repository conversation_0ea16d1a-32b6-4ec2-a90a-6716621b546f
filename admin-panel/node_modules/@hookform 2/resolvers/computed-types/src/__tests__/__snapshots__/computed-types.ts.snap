// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`computedTypesResolver should return a single error from computedTypesResolver when validation fails 1`] = `
Object {
  "errors": Object {
    "address": Object {
      "city": Object {
        "message": "Is required",
        "ref": undefined,
        "type": "ValidationError",
      },
      "zipCode": Object {
        "message": "Must be 5 characters long",
        "ref": undefined,
        "type": "ValidationError",
      },
    },
    "birthYear": Object {
      "message": "Expect value to be \\"number\\"",
      "ref": undefined,
      "type": "ValidationError",
    },
    "email": Object {
      "message": "Incorrect email",
      "ref": Object {
        "name": "email",
      },
      "type": "ValidationError",
    },
    "enabled": Object {
      "message": "Expect value to be \\"boolean\\"",
      "ref": undefined,
      "type": "ValidationError",
    },
    "like": Object {
      "id": Object {
        "message": "Expect value to be \\"number\\"",
        "ref": undefined,
        "type": "ValidationError",
      },
      "name": Object {
        "message": "Expect value to be \\"string\\"",
        "ref": undefined,
        "type": "ValidationError",
      },
    },
    "password": Object {
      "message": "One uppercase character",
      "ref": Object {
        "name": "password",
      },
      "type": "ValidationError",
    },
    "repeatPassword": Object {
      "message": "Expect value to be \\"string\\"",
      "ref": undefined,
      "type": "ValidationError",
    },
    "tags": Object {
      "message": "Expecting value to be an array",
      "ref": undefined,
      "type": "ValidationError",
    },
    "username": Object {
      "message": "Expect value to be \\"string\\"",
      "ref": Object {
        "name": "username",
      },
      "type": "ValidationError",
    },
  },
  "values": Object {},
}
`;
